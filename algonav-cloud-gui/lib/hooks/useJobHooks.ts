import { useState, useEffect, useMemo, useCallback } from 'react';
import { Task, SortConfig } from '../../types/job';
import {
  downloadFile,
  downloadTaskFiles,
  downloadBatchFilesByType,
  downloadAllBatchFiles,
  submitSupportRequest
} from '../../services/jobService';

/**
 * Custom hook for managing tasks state (filtering, sorting)
 */
export const useTasksState = (initialTasks: Task[]) => {
  const [tasks, setTasks] = useState<Task[]>(initialTasks);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>(['all']);
  const [orderBy, setOrderBy] = useState<string>(() => {
    return localStorage.getItem('jobTableOrderBy') || 'dataset';
  });
  const [order, setOrder] = useState<'asc' | 'desc'>(() => {
    return (localStorage.getItem('jobTableOrder') as 'asc' | 'desc') || 'asc';
  });
  const [selectedTaskIds, setSelectedTaskIds] = useState<number[]>([]);

  // Update tasks when initialTasks changes
  useEffect(() => {
    setTasks(initialTasks);
  }, [initialTasks]);

  // Save sorting preferences to localStorage
  useEffect(() => {
    localStorage.setItem('jobTableOrder', order);
    localStorage.setItem('jobTableOrderBy', orderBy);
  }, [order, orderBy]);

  // Filter tasks based on search term and selected statuses
  useEffect(() => {
    // Removed DEBUG logs
    let filteredTasks = [...initialTasks];
    
    // Filter out bulk tasks (tasks with a bulk_job_type)
    const tasksBeforeBulkFilter = [...filteredTasks]; // Keep this line if needed elsewhere, otherwise remove if only for logging
    filteredTasks = filteredTasks.filter(task => {
      const isBulk = !!task.bulk_job_type;
      // Removed DEBUG log
      return !isBulk;
    });
    // Removed DEBUG logs

    // Apply search term filter
    if (searchTerm.trim()) {
      filteredTasks = filteredTasks.filter(task =>
        task.id.toString().includes(searchTerm) ||
        task.dataset?.name?.toLowerCase().includes(searchTerm) ||
        task.dataset?.description?.toLowerCase().includes(searchTerm) ||
        task.status.toLowerCase().includes(searchTerm) ||
        task.global_job_template?.name?.toLowerCase().includes(searchTerm) ||
        task.task_results?.some(result =>
          result.file_name?.toLowerCase().includes(searchTerm) ||
          result.file_type?.toLowerCase().includes(searchTerm)
        ) ||
        task.created_at?.toString().toLowerCase().includes(searchTerm)
      );
    }
    
    // Apply status filter
    if (!selectedStatuses.includes('all')) {
      filteredTasks = filteredTasks.filter(task => 
        selectedStatuses.some(status => task.status.toLowerCase() === status.toLowerCase())
      );
    }
    
    // Apply sorting
    filteredTasks.sort((a, b) => {
      let aValue = a[orderBy as keyof Task];
      let bValue = b[orderBy as keyof Task];

      if (orderBy === 'dataset') {
        aValue = a.dataset?.name || '';
        bValue = b.dataset?.name || '';
      }

      if (!aValue) return 1;
      if (!bValue) return -1;

      const comparison = String(aValue).localeCompare(String(bValue));
      return order === 'asc' ? comparison : -comparison;
    });
    // Removed DEBUG log
    setTasks(filteredTasks);
  }, [initialTasks, searchTerm, selectedStatuses, orderBy, order]);
  // Line 95 removed as it was an erroneous duplication

  // Handle sort column click
  const handleSort = useCallback((property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    const newOrder = isAsc ? 'desc' : 'asc';
    setOrder(newOrder);
    setOrderBy(property);
  }, [orderBy, order]);

  // Handle search input change
  const handleSearch = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = event.target.value.toLowerCase();
    setSearchTerm(searchTerm);
  }, []);

  // Handle status filter change
  const handleStatusFilterChange = useCallback((statuses: string[]) => {
    setSelectedStatuses(statuses);
  }, []);

  // Toggle task selection
  const toggleTaskSelection = useCallback((taskId: number) => {
    setSelectedTaskIds(prev => {
      if (prev.includes(taskId)) {
        return prev.filter(id => id !== taskId);
      } else {
        return [...prev, taskId];
      }
    });
  }, []);

  // Toggle all tasks selection
  const toggleAllTasksSelection = useCallback(() => {
    if (selectedTaskIds.length === tasks.length) {
      setSelectedTaskIds([]);
    } else {
      setSelectedTaskIds(tasks.map(task => task.id));
    }
  }, [tasks, selectedTaskIds]);

  return {
    tasks,
    searchTerm,
    selectedStatuses,
    orderBy,
    order,
    selectedTaskIds,
    handleSort,
    handleSearch,
    handleStatusFilterChange,
    toggleTaskSelection,
    toggleAllTasksSelection,
    setTasks
  };
};

/**
 * Custom hook for managing download operations
 */
export const useDownloadState = (jobId: string) => { // Renamed batchId to jobId
  const [downloadingFiles, setDownloadingFiles] = useState<Record<string, boolean>>({});
  const [globalDownloading, setGlobalDownloading] = useState(false);

  // Helper to create a download link
  const createDownloadLink = useCallback((blob: Blob, fileName: string) => {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }, []);

  // Download a single file
  const handleDownloadFile = useCallback(async (taskId: number, result: { id: string; file_name: string }) => {
    if (!result || !result.id) return;
    
    const fileId = `${taskId}-${result.id}`;
    setDownloadingFiles(prev => ({ ...prev, [fileId]: true }));
    
    try {
      const blob = await downloadFile(taskId, result.id);
      createDownloadLink(blob, result.file_name);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw error;
    } finally {
      setDownloadingFiles(prev => ({ ...prev, [fileId]: false }));
    }
  }, [createDownloadLink]);

  // Download all files for a task
  const handleDownloadTaskFiles = useCallback(async (taskId: number) => {
    const taskKey = `task-${taskId}`;
    setDownloadingFiles(prev => ({ ...prev, [taskKey]: true }));
    
    try {
      const blob = await downloadTaskFiles(taskId);
      createDownloadLink(blob, `task_${taskId}_files.zip`);
    } catch (error) {
      console.error('Error downloading task files:', error);
      throw error;
    } finally {
      setDownloadingFiles(prev => ({ ...prev, [taskKey]: false }));
    }
  }, [createDownloadLink]);

  // Download all files of a specific type
  const handleDownloadAllOfType = useCallback(async (type: string) => {
    const typeKey = `type-${type}`;
    setDownloadingFiles(prev => ({ ...prev, [typeKey]: true }));
    
    try {
      const blob = await downloadBatchFilesByType(jobId, type); // Pass jobId
      createDownloadLink(blob, `job_${jobId}_${type}.zip`); // Update filename
    } catch (error) {
      console.error(`Error downloading ${type} files:`, error);
      throw error;
    } finally {
      setDownloadingFiles(prev => ({ ...prev, [typeKey]: false }));
    }
  }, [jobId, createDownloadLink]); // Update dependency

  // Download all files in the job (formerly batch)
  const handleDownloadAll = useCallback(async () => {
    setDownloadingFiles(prev => ({ ...prev, all: true }));
    
    try {
      const blob = await downloadAllBatchFiles(jobId); // Pass jobId
      createDownloadLink(blob, `job_${jobId}_all_files.zip`); // Update filename
    } catch (error) {
      console.error('Error downloading all files:', error);
      throw error;
    } finally {
      setDownloadingFiles(prev => ({ ...prev, all: false }));
    }
  }, [jobId, createDownloadLink]); // Update dependency

  // Check if a specific file is currently downloading
  const isDownloading = useCallback((key: string) => {
    return !!downloadingFiles[key];
  }, [downloadingFiles]);

  return {
    downloadingFiles,
    globalDownloading,
    handleDownloadFile,
    handleDownloadTaskFiles,
    handleDownloadAllOfType,
    handleDownloadAll,
    isDownloading
  };
};

/**
 * Custom hook for managing support dialog
 */
export const useSupportDialog = () => {
  const [supportDialogOpen, setSupportDialogOpen] = useState(false);
  const [currentTaskId, setCurrentTaskId] = useState<number | null>(null);
  const [supportReason, setSupportReason] = useState<string>('');
  const [additionalInfo, setAdditionalInfo] = useState('');
  const [supportType, setSupportType] = useState<'dataset' | 'job' | 'general'>('dataset');
  const [datasetName, setDatasetName] = useState<string>('');
  const [jobId, setJobId] = useState<string>('');

  // Open support dialog for a specific dataset
  const handleOpenSupportDialog = useCallback((taskId: number, dataset?: string, jobId?: string) => {
    setCurrentTaskId(taskId);
    setDatasetName(dataset || '');
    setJobId(jobId || '');
    setSupportReason('processing_terminated');
    setAdditionalInfo('');
    setSupportType('dataset');
    setSupportDialogOpen(true);
  }, []);

  // Open support dialog for a complete job
  const handleOpenJobSupportDialog = useCallback((jobId?: string) => {
    // jobId is optional, for future use if needed
    setCurrentTaskId(null);
    setDatasetName('');
    setJobId(jobId || '');
    setSupportReason('most_failed');
    setAdditionalInfo('');
    setSupportType('job');
    setSupportDialogOpen(true);
  }, []);

  // Open general support dialog
  const handleOpenGeneralSupportDialog = useCallback(() => {
    setCurrentTaskId(null);
    setDatasetName('');
    setJobId('');
    setSupportReason('account_question');
    setAdditionalInfo('');
    setSupportType('general');
    setSupportDialogOpen(true);
  }, []);

  const handleCloseSupportDialog = useCallback(() => {
    setSupportDialogOpen(false);
  }, []);

  const handleSupportReasonChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSupportReason(event.target.value);
  }, []);

  const handleAdditionalInfoChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setAdditionalInfo(event.target.value);
  }, []);

  const handleSubmitSupport = useCallback(async () => {
    try {
      // Send different data based on support type
      let result;
      
      if (supportType === 'dataset' && currentTaskId !== null) {
        // Dataset-specific support request
        result = await submitSupportRequest(
          currentTaskId, 
          supportReason, 
          additionalInfo,
          'dataset'
        );
      } else if (supportType === 'job') {
        // Job-wide support request
        result = await submitSupportRequest(
          null, 
          supportReason, 
          additionalInfo,
          'job'
        );
      } else {
        // General support request
        result = await submitSupportRequest(
          null, 
          supportReason, 
          additionalInfo,
          'general'
        );
      }
      
      handleCloseSupportDialog();
      return result;
    } catch (error) {
      console.error('Error submitting support request:', error);
      throw error;
    }
  }, [currentTaskId, supportReason, additionalInfo, supportType, handleCloseSupportDialog]);

  return {
    supportDialogOpen,
    currentTaskId,
    supportReason,
    additionalInfo,
    supportType,
    datasetName,
    jobId,
    handleOpenSupportDialog,
    handleOpenJobSupportDialog,
    handleOpenGeneralSupportDialog,
    handleCloseSupportDialog,
    handleSupportReasonChange,
    handleAdditionalInfoChange,
    handleSubmitSupport
  };
};

/**
 * Custom hook for managing notifications
 */
export const useNotifications = () => {
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'info' | 'warning' | 'error'>('success');

  const showNotification = useCallback((message: string, severity: 'success' | 'info' | 'warning' | 'error' = 'success') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  }, []);

  const handleCloseSnackbar = useCallback((event?: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setSnackbarOpen(false);
  }, []);

  return {
    snackbarOpen,
    snackbarMessage,
    snackbarSeverity,
    showNotification,
    handleCloseSnackbar
  };
};
