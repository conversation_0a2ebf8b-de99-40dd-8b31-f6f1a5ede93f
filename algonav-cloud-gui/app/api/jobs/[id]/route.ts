import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const { id } = params;

    // Step 1: Fetch batch details without jobs
    const { data: batchData, error: batchError } = await supabase
        .from('jobs')
        .select(`*`) // Select all batch fields
        .eq('id', id)
        .eq('user_id', userId)
        .single();

    if (batchError) {
        console.error("Error fetching batch:", batchError);
        return NextResponse.json({ error: batchError.message }, { status: 500 });
    }

    if (!batchData) {
        return NextResponse.json({ error: "Batch not found" }, { status: 404 });
    }

    // Step 2: Fetch associated non-bulk jobs separately
    const { data: tasksData, error: tasksError } = await supabase
        .from('tasks')
        .select(`
            id,
            global_job_template:global_job_templates(name),
            dataset:datasets(name, description)
        `)
        .eq('job_id', id)
        .eq('user_id', userId)
        .is('bulk_job_type', null); // Filter out bulk jobs

    if (tasksError) {
        console.error("Error fetching jobs for batch:", tasksError);
        // Decide if we should return partial data or error out
        // For now, let's return the batch data but indicate job fetch error
         return NextResponse.json({
             success: true,
             data: { ...batchData, jobs: [] }, // Return batch data with empty jobs array
             warning: 'Could not fetch associated jobs: ' + tasksError.message
         });
        // Alternatively, error out completely:
        // return NextResponse.json({ error: `Failed to fetch jobs: ${jobsError.message}` }, { status: 500 });
    }

    // Step 3: Combine batch data and jobs data
    const combinedData = {
        ...batchData,
        tasks: tasksData || [] // Ensure jobs is always an array
    };

    return NextResponse.json({ success: true, data: combinedData });
});
